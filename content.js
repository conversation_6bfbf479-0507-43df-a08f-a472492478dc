// Content script for Stealth Assistant
class StealthUI {
  constructor() {
    this.isActive = false;
    this.overlay = null;
    this.inputBox = null;
    this.responseBox = null;
    this.setupEventListeners();
    this.createStealthOverlay();
  }

  setupEventListeners() {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
    });

    // Additional hotkeys for stealth operation
    document.addEventListener('keydown', (e) => {
      // Triple-tap Escape for emergency hide
      if (e.key === 'Escape') {
        this.handleEscapeKey();
      }
      
      // Ctrl+Shift+H for quick hide
      if (e.ctrlKey && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        this.hideAll();
      }
    });

    // Hide on window focus change (screen sharing detection)
    window.addEventListener('blur', () => {
      this.hideAll();
    });

    // Hide on visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.hideAll();
      }
    });
  }

  createStealthOverlay() {
    // Create invisible overlay container
    this.overlay = document.createElement('div');
    this.overlay.id = 'stealth-assistant-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      pointer-events: none;
      background: transparent;
      display: none;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    // Create input box
    this.inputBox = document.createElement('div');
    this.inputBox.style.cssText = `
      position: absolute;
      top: 20px;
      right: 20px;
      width: 300px;
      background: rgba(0, 0, 0, 0.9);
      border: 1px solid #333;
      border-radius: 8px;
      padding: 15px;
      color: white;
      font-size: 14px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      pointer-events: auto;
      display: none;
    `;

    const input = document.createElement('textarea');
    input.placeholder = 'Ask your question...';
    input.style.cssText = `
      width: 100%;
      height: 60px;
      background: #222;
      border: 1px solid #444;
      border-radius: 4px;
      color: white;
      padding: 8px;
      font-size: 13px;
      resize: none;
      outline: none;
    `;

    const submitBtn = document.createElement('button');
    submitBtn.textContent = 'Ask';
    submitBtn.style.cssText = `
      background: #007acc;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      margin-top: 8px;
      cursor: pointer;
      font-size: 12px;
    `;

    submitBtn.onclick = () => this.submitQuery(input.value);
    
    // Enter key to submit
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.submitQuery(input.value);
      }
    });

    this.inputBox.appendChild(input);
    this.inputBox.appendChild(submitBtn);

    // Create response box
    this.responseBox = document.createElement('div');
    this.responseBox.style.cssText = `
      position: absolute;
      top: 20px;
      left: 20px;
      max-width: 400px;
      background: rgba(0, 50, 0, 0.95);
      border: 1px solid #0a5d0a;
      border-radius: 8px;
      padding: 15px;
      color: #90ee90;
      font-size: 13px;
      line-height: 1.4;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      pointer-events: auto;
      display: none;
      max-height: 300px;
      overflow-y: auto;
    `;

    this.overlay.appendChild(this.inputBox);
    this.overlay.appendChild(this.responseBox);
    document.body.appendChild(this.overlay);
  }

  handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'toggle-assistant':
        this.toggleAssistant(request.isActive);
        break;
        
      case 'show-quick-input':
        this.showQuickInput();
        break;
    }
  }

  toggleAssistant(isActive) {
    this.isActive = isActive;
    if (isActive) {
      this.showInputBox();
    } else {
      this.hideAll();
    }
  }

  showQuickInput() {
    this.showInputBox();
    const input = this.inputBox.querySelector('textarea');
    input.focus();
  }

  showInputBox() {
    this.overlay.style.display = 'block';
    this.inputBox.style.display = 'block';
    const input = this.inputBox.querySelector('textarea');
    input.value = '';
    input.focus();
  }

  hideAll() {
    if (this.overlay) {
      this.overlay.style.display = 'none';
      this.inputBox.style.display = 'none';
      this.responseBox.style.display = 'none';
    }
    this.isActive = false;
  }

  async submitQuery(query) {
    if (!query.trim()) return;

    const input = this.inputBox.querySelector('textarea');
    const submitBtn = this.inputBox.querySelector('button');

    // Show loading state
    submitBtn.textContent = 'Thinking...';
    submitBtn.disabled = true;
    input.disabled = true;

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'get-ai-response',
        query: query
      });

      if (response.success) {
        this.showResponse(response.response);
        input.value = '';
        this.inputBox.style.display = 'none';
      } else {
        // Enhanced error display
        this.showErrorResponse(response.error);
      }
    } catch (error) {
      this.showErrorResponse(`Connection error: ${error.message}`);
    }

    // Reset button and input
    submitBtn.textContent = 'Ask';
    submitBtn.disabled = false;
    input.disabled = false;
  }

  showErrorResponse(errorMessage) {
    let userFriendlyMessage = errorMessage;

    // Make error messages more user-friendly
    if (errorMessage.includes('Rate limit')) {
      userFriendlyMessage = '⏳ Rate limit reached. Please wait a moment and try again.';
    } else if (errorMessage.includes('Invalid API key')) {
      userFriendlyMessage = '🔑 Invalid API key. Please check your settings.';
    } else if (errorMessage.includes('API key not configured')) {
      userFriendlyMessage = '⚙️ Please configure your API key in the extension settings first.';
    } else if (errorMessage.includes('temporarily unavailable')) {
      userFriendlyMessage = '🔧 AI service temporarily unavailable. Try again in a few minutes.';
    }

    this.responseBox.innerHTML = `
      <div style="margin-bottom: 10px; font-weight: bold; color: #ff7f7f;">⚠️ Error</div>
      <div style="color: #ffaaaa;">${userFriendlyMessage}</div>
      <div style="margin-top: 10px; font-size: 11px; color: #666;">
        Press Escape to hide • Check extension settings if needed
      </div>
    `;
    this.responseBox.style.display = 'block';
    this.responseBox.style.background = 'rgba(50, 0, 0, 0.95)';
    this.responseBox.style.borderColor = '#5d0a0a';

    // Auto-hide error after 15 seconds
    setTimeout(() => {
      if (this.responseBox.style.display === 'block') {
        this.responseBox.style.display = 'none';
        // Reset styling for next use
        this.responseBox.style.background = 'rgba(0, 50, 0, 0.95)';
        this.responseBox.style.borderColor = '#0a5d0a';
      }
    }, 15000);
  }

  showResponse(text) {
    this.responseBox.innerHTML = `
      <div style="margin-bottom: 10px; font-weight: bold; color: #7fff7f;">AI Response:</div>
      <div>${text.replace(/\n/g, '<br>')}</div>
      <div style="margin-top: 10px; font-size: 11px; color: #666;">
        Press Escape to hide • Ctrl+Shift+H to hide all
      </div>
    `;
    this.responseBox.style.display = 'block';

    // Auto-hide after 30 seconds
    setTimeout(() => {
      if (this.responseBox.style.display === 'block') {
        this.responseBox.style.display = 'none';
      }
    }, 30000);
  }

  handleEscapeKey() {
    // Progressive hiding on escape
    if (this.responseBox.style.display === 'block') {
      this.responseBox.style.display = 'none';
    } else if (this.inputBox.style.display === 'block') {
      this.inputBox.style.display = 'none';
    } else {
      this.hideAll();
    }
  }
}

// Initialize stealth UI when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new StealthUI();
  });
} else {
  new StealthUI();
}
