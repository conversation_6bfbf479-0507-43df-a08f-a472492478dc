# 🔧 Troubleshooting Guide - Stealth Assistant

## Common Issues and Solutions

### 🚨 API Connection Issues

#### Error: "Rate limit exceeded (429)"
**Cause**: Too many API requests in a short time period.

**Solutions**:
1. **Wait and retry**: The extension now has automatic retry logic with exponential backoff
2. **Check your API plan**: Free tier accounts have lower rate limits
3. **Reduce usage frequency**: Space out your queries by at least 10-15 seconds
4. **Upgrade API plan**: Consider upgrading to a paid plan for higher limits

**Rate Limits by Provider**:
- OpenAI Free Tier: 3 requests/minute, 200 requests/day
- OpenAI Paid Tier: 3,500 requests/minute
- Anthropic: Varies by plan

#### Error: "Invalid API key (401)"
**Solutions**:
1. Double-check your API key in the extension settings
2. Ensure no extra spaces before/after the key
3. Verify the key is active in your AI provider dashboard
4. For OpenAI: Check at https://platform.openai.com/api-keys
5. For Anthropic: Check at https://console.anthropic.com/

#### Error: "API access forbidden (403)"
**Solutions**:
1. Check if your API key has the necessary permissions
2. Verify your account has sufficient credits/balance
3. Some keys may be restricted to certain models or features

### ⌨️ Hotkey Issues

#### Hotkeys not working
**Solutions**:
1. **Check for conflicts**: Other extensions or software might use the same shortcuts
2. **Try alternative shortcuts**: 
   - Windows: Try `Alt+Shift+A` instead of `Ctrl+Shift+A`
   - Mac: Try `Option+Shift+A` instead of `Cmd+Shift+A`
3. **Reload the extension**: Go to `chrome://extensions/` and reload
4. **Check permissions**: Ensure the extension has necessary permissions

#### Partial hotkey functionality
**Solutions**:
1. Test each hotkey individually
2. Check if the page has focus (click on the page first)
3. Some websites may block certain key combinations

### 🖥️ Stealth Mode Issues

#### UI visible during screen sharing
**Causes & Solutions**:
1. **Screen capture software differences**: 
   - Test with your specific software (Zoom, Teams, etc.)
   - Some capture overlays, others don't
2. **Browser zoom level**: Reset zoom to 100%
3. **Multiple monitors**: Position overlays on non-shared screen
4. **Hardware acceleration**: Try disabling in Chrome settings

#### Emergency hide not working
**Solutions**:
1. **Practice the shortcuts**: `Ctrl+Shift+H` or triple-tap `Escape`
2. **Use window switching**: `Alt+Tab` to switch away quickly
3. **Browser minimize**: `Ctrl+M` or click minimize button

### 🔧 Extension Loading Issues

#### Extension not loading
**Solutions**:
1. **Enable Developer Mode**: In `chrome://extensions/`
2. **Check file permissions**: Ensure all files are readable
3. **Reload extension**: Click the reload button in extensions page
4. **Check console errors**: Open DevTools and look for errors

#### Content script not injecting
**Solutions**:
1. **Refresh the page**: The extension needs to reload on existing tabs
2. **Check website CSP**: Some sites block content scripts
3. **Try different websites**: Test on multiple sites

### 🌐 Website Compatibility

#### Extension not working on specific sites
**Common problematic sites**:
- Banking websites (high security)
- Government sites
- Sites with strict Content Security Policy

**Solutions**:
1. **Test on different sites**: Try Google, Wikipedia, news sites
2. **Check browser console**: Look for CSP or security errors
3. **Use on less restrictive sites**: Avoid high-security domains

### 📱 Performance Issues

#### Slow response times
**Solutions**:
1. **Check internet connection**: Ensure stable connection
2. **Reduce query length**: Shorter queries = faster responses
3. **Choose faster models**: GPT-3.5 is faster than GPT-4
4. **Clear browser cache**: May help with performance

#### High memory usage
**Solutions**:
1. **Clear extension data**: Use "Clear All Data" in settings
2. **Restart browser**: Close and reopen Chrome
3. **Disable other extensions**: Test with minimal extensions

### 🔐 Security and Privacy

#### API key security concerns
**Best practices**:
1. **Use environment-specific keys**: Don't use production keys for testing
2. **Monitor usage**: Check your API provider dashboard regularly
3. **Rotate keys**: Change keys periodically
4. **Limit permissions**: Use keys with minimal necessary permissions

#### Data privacy
**What the extension stores**:
- API keys (encrypted in local storage)
- API provider preference
- No query history or responses are stored

### 🧪 Testing and Debugging

#### Debug mode activation
1. **Open DevTools**: Right-click → Inspect
2. **Go to Console tab**: Look for extension messages
3. **Check Network tab**: Monitor API requests
4. **Extension DevTools**: Go to `chrome://extensions/` → Details → Inspect views

#### Common console errors
```
Error: "Cannot read property of undefined"
Solution: Reload the extension and refresh the page

Error: "CSP violation"
Solution: The website blocks the extension, try a different site

Error: "Failed to fetch"
Solution: Check internet connection and API key
```

### 📞 Getting Help

#### Before seeking help:
1. ✅ Check this troubleshooting guide
2. ✅ Test on multiple websites
3. ✅ Verify API key and internet connection
4. ✅ Check browser console for errors
5. ✅ Try reloading the extension

#### Information to provide:
- Browser version and OS
- Error messages (exact text)
- Steps to reproduce the issue
- Which websites you tested on
- API provider being used

### 🔄 Quick Fixes Checklist

When something isn't working:

1. **Reload extension** (`chrome://extensions/` → reload button)
2. **Refresh the webpage** (F5 or Ctrl+R)
3. **Check API key** (extension popup → test connection)
4. **Try different website** (test on google.com)
5. **Check hotkeys** (try clicking extension icon instead)
6. **Clear data** (extension popup → Clear All Data)
7. **Restart browser** (close and reopen Chrome)

### 🚀 Performance Optimization

#### For better stealth performance:
1. **Use shorter queries**: Under 50 words when possible
2. **Position overlays strategically**: Away from main content areas
3. **Practice emergency procedures**: Know your hide shortcuts
4. **Test regularly**: Verify stealth features before important use

#### For better response times:
1. **Use GPT-3.5 instead of GPT-4**: Faster responses
2. **Keep queries concise**: Specific questions get better answers
3. **Stable internet**: Use wired connection when possible
4. **Close unnecessary tabs**: Reduce browser load

---

**Remember**: This extension is for educational purposes. Always ensure compliance with your institution's policies and local regulations.
