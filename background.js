// Background service worker for Stealth Assistant
class StealthAssistant {
  constructor() {
    this.apiKey = null;
    this.isActive = false;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      console.log('Stealth Assistant installed');
    });

    // Handle hotkey commands
    chrome.commands.onCommand.addListener((command) => {
      this.handleCommand(command);
    });

    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleCommand(command) {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    switch (command) {
      case 'activate-assistant':
        chrome.tabs.sendMessage(tab.id, { 
          action: 'toggle-assistant',
          isActive: !this.isActive 
        });
        this.isActive = !this.isActive;
        break;
        
      case 'quick-query':
        chrome.tabs.sendMessage(tab.id, { 
          action: 'show-quick-input' 
        });
        break;
    }
  }

  async handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'get-ai-response':
        try {
          const response = await this.getAIResponse(request.query);
          sendResponse({ success: true, response });
        } catch (error) {
          sendResponse({ success: false, error: error.message });
        }
        break;

      case 'save-api-key':
        await this.saveApiKey(request.apiKey);
        sendResponse({ success: true });
        break;

      case 'get-api-key':
        const apiKey = await this.getApiKey();
        sendResponse({ success: true, apiKey });
        break;
    }
  }

  async getAIResponse(query) {
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('API key not configured');
    }

    // Using OpenAI API as example - can be switched to other providers
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant providing concise, accurate answers for academic and professional contexts. Keep responses brief and to the point.'
          },
          {
            role: 'user',
            content: query
          }
        ],
        max_tokens: 150,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async saveApiKey(apiKey) {
    await chrome.storage.local.set({ 'ai_api_key': apiKey });
    this.apiKey = apiKey;
  }

  async getApiKey() {
    if (this.apiKey) return this.apiKey;
    
    const result = await chrome.storage.local.get(['ai_api_key']);
    this.apiKey = result.ai_api_key;
    return this.apiKey;
  }
}

// Initialize the assistant
new StealthAssistant();
