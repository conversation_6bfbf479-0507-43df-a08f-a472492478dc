// Background service worker for Stealth Assistant
class StealthAssistant {
  constructor() {
    this.apiKey = null;
    this.isActive = false;
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(() => {
      console.log('Stealth Assistant installed');
    });

    // Handle hotkey commands
    chrome.commands.onCommand.addListener((command) => {
      this.handleCommand(command);
    });

    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  async handleCommand(command) {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    switch (command) {
      case 'activate-assistant':
        chrome.tabs.sendMessage(tab.id, { 
          action: 'toggle-assistant',
          isActive: !this.isActive 
        });
        this.isActive = !this.isActive;
        break;
        
      case 'quick-query':
        chrome.tabs.sendMessage(tab.id, { 
          action: 'show-quick-input' 
        });
        break;
    }
  }

  async handleMessage(request, sender, sendResponse) {
    switch (request.action) {
      case 'get-ai-response':
        try {
          const response = await this.getAIResponse(request.query);
          sendResponse({ success: true, response });
        } catch (error) {
          sendResponse({ success: false, error: error.message });
        }
        break;

      case 'save-api-key':
        await this.saveApiKey(request.apiKey);
        sendResponse({ success: true });
        break;

      case 'get-api-key':
        const apiKey = await this.getApiKey();
        sendResponse({ success: true, apiKey });
        break;
    }
  }

  async getAIResponse(query) {
    const apiKey = await this.getApiKey();
    if (!apiKey) {
      throw new Error('API key not configured. Please set your API key in the extension settings.');
    }

    const provider = await this.getApiProvider();

    try {
      // Add retry logic for rate limiting
      return await this.makeAPIRequestWithRetry(query, apiKey, provider);
    } catch (error) {
      // Enhanced error handling
      if (error.message.includes('429')) {
        throw new Error('Rate limit exceeded. Please wait a moment and try again.');
      } else if (error.message.includes('401')) {
        throw new Error('Invalid API key. Please check your API key in settings.');
      } else if (error.message.includes('403')) {
        throw new Error('API access forbidden. Check your API key permissions.');
      } else if (error.message.includes('500')) {
        throw new Error('AI service temporarily unavailable. Please try again later.');
      } else {
        throw new Error(`AI service error: ${error.message}`);
      }
    }
  }

  async makeAPIRequestWithRetry(query, apiKey, provider, retryCount = 0) {
    const maxRetries = 2;
    const baseDelay = 1000; // 1 second

    try {
      return await this.makeAPIRequest(query, apiKey, provider);
    } catch (error) {
      if (error.message.includes('429') && retryCount < maxRetries) {
        // Exponential backoff for rate limiting
        const delay = baseDelay * Math.pow(2, retryCount);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeAPIRequestWithRetry(query, apiKey, provider, retryCount + 1);
      }
      throw error;
    }
  }

  async makeAPIRequest(query, apiKey, provider = 'openai') {
    let url, headers, body;

    switch (provider) {
      case 'openai':
        url = 'https://api.openai.com/v1/chat/completions';
        headers = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        };
        body = JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant providing concise, accurate answers. Keep responses brief (under 100 words) and to the point.'
            },
            {
              role: 'user',
              content: query
            }
          ],
          max_tokens: 120,
          temperature: 0.7
        });
        break;

      case 'anthropic':
        url = 'https://api.anthropic.com/v1/messages';
        headers = {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01'
        };
        body = JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 120,
          messages: [
            {
              role: 'user',
              content: `Please provide a concise, helpful answer (under 100 words): ${query}`
            }
          ]
        });
        break;

      default:
        throw new Error('Unsupported AI provider');
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: body
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`${response.status}: ${errorText}`);
    }

    const data = await response.json();

    // Extract response based on provider
    if (provider === 'openai') {
      return data.choices[0].message.content;
    } else if (provider === 'anthropic') {
      return data.content[0].text;
    }
  }

  async saveApiKey(apiKey) {
    await chrome.storage.local.set({ 'ai_api_key': apiKey });
    this.apiKey = apiKey;
  }

  async getApiKey() {
    if (this.apiKey) return this.apiKey;

    const result = await chrome.storage.local.get(['ai_api_key']);
    this.apiKey = result.ai_api_key;
    return this.apiKey;
  }

  async getApiProvider() {
    const result = await chrome.storage.local.get(['api_provider']);
    return result.api_provider || 'openai';
  }
}

// Initialize the assistant
new StealthAssistant();
