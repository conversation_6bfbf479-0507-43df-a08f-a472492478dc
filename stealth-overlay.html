<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stealth Assistant Overlay</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .stealth-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999999;
        }
        
        .input-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 320px;
            background: rgba(20, 20, 20, 0.95);
            border: 1px solid #333;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
            pointer-events: auto;
            backdrop-filter: blur(10px);
        }
        
        .response-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            max-width: 420px;
            background: rgba(0, 40, 0, 0.95);
            border: 1px solid #0a5d0a;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
            pointer-events: auto;
            backdrop-filter: blur(10px);
            max-height: 350px;
            overflow-y: auto;
        }
        
        .input-field {
            width: 100%;
            height: 70px;
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 6px;
            color: #fff;
            padding: 10px;
            font-size: 14px;
            resize: none;
            outline: none;
            font-family: inherit;
        }
        
        .input-field::placeholder {
            color: #888;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #007acc, #005a9e);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin-top: 10px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: background 0.2s;
        }
        
        .submit-btn:hover {
            background: linear-gradient(135deg, #0086e6, #006bb3);
        }
        
        .submit-btn:disabled {
            background: #555;
            cursor: not-allowed;
        }
        
        .response-text {
            color: #90ee90;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .response-header {
            color: #7fff7f;
            font-weight: 600;
            margin-bottom: 12px;
            font-size: 13px;
        }
        
        .response-footer {
            margin-top: 12px;
            font-size: 11px;
            color: #666;
            font-style: italic;
        }
        
        .hidden {
            display: none !important;
        }
        
        /* Stealth mode - completely invisible */
        .stealth-mode {
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.1s;
        }
        
        /* Quick access indicator */
        .quick-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 8px;
            height: 8px;
            background: #007acc;
            border-radius: 50%;
            opacity: 0.3;
            pointer-events: none;
            z-index: 999998;
        }
    </style>
</head>
<body>
    <div class="stealth-container" id="stealthContainer">
        <!-- Input Panel -->
        <div class="input-panel hidden" id="inputPanel">
            <textarea 
                class="input-field" 
                id="queryInput" 
                placeholder="Ask your question discreetly..."
                maxlength="500">
            </textarea>
            <button class="submit-btn" id="submitBtn">Ask AI</button>
        </div>
        
        <!-- Response Panel -->
        <div class="response-panel hidden" id="responsePanel">
            <div class="response-header">AI Assistant</div>
            <div class="response-text" id="responseText"></div>
            <div class="response-footer">Press ESC to hide • Auto-hide in <span id="countdown">30</span>s</div>
        </div>
    </div>
    
    <!-- Quick access indicator (optional) -->
    <div class="quick-indicator hidden" id="quickIndicator"></div>
    
    <script>
        // This overlay can be used for more complex UI interactions
        // The main functionality is handled in content.js
        
        class StealthOverlay {
            constructor() {
                this.container = document.getElementById('stealthContainer');
                this.inputPanel = document.getElementById('inputPanel');
                this.responsePanel = document.getElementById('responsePanel');
                this.queryInput = document.getElementById('queryInput');
                this.submitBtn = document.getElementById('submitBtn');
                this.responseText = document.getElementById('responseText');
                this.countdown = document.getElementById('countdown');
                this.quickIndicator = document.getElementById('quickIndicator');
                
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                // Handle form submission
                this.submitBtn.addEventListener('click', () => {
                    this.submitQuery();
                });
                
                this.queryInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.submitQuery();
                    }
                    
                    if (e.key === 'Escape') {
                        this.hide();
                    }
                });
                
                // Emergency hide on window events
                window.addEventListener('blur', () => this.emergencyHide());
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) this.emergencyHide();
                });
            }
            
            show() {
                this.inputPanel.classList.remove('hidden');
                this.queryInput.focus();
            }
            
            hide() {
                this.inputPanel.classList.add('hidden');
                this.responsePanel.classList.add('hidden');
            }
            
            emergencyHide() {
                this.container.classList.add('stealth-mode');
                setTimeout(() => {
                    this.hide();
                    this.container.classList.remove('stealth-mode');
                }, 100);
            }
            
            submitQuery() {
                const query = this.queryInput.value.trim();
                if (!query) return;
                
                // This would integrate with the main content script
                // For now, just show a placeholder response
                this.showResponse("This is a placeholder response. Integration with AI service needed.");
            }
            
            showResponse(text) {
                this.responseText.innerHTML = text.replace(/\n/g, '<br>');
                this.responsePanel.classList.remove('hidden');
                this.inputPanel.classList.add('hidden');
                
                // Start countdown
                let timeLeft = 30;
                const countdownInterval = setInterval(() => {
                    timeLeft--;
                    this.countdown.textContent = timeLeft;
                    
                    if (timeLeft <= 0) {
                        clearInterval(countdownInterval);
                        this.responsePanel.classList.add('hidden');
                    }
                }, 1000);
            }
        }
        
        // Initialize overlay
        const overlay = new StealthOverlay();
        
        // Expose to parent window if needed
        window.stealthOverlay = overlay;
    </script>
</body>
</html>
