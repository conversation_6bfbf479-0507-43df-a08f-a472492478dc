# 🥷 Stealth Assistant Chrome Extension

A discreet AI-powered assistant designed for personal use during interviews, exams, or study sessions. This extension operates invisibly during screen sharing and provides AI-generated responses through hidden interfaces.

## ⚠️ Important Disclaimer

**This extension is for personal educational use only.** Please ensure compliance with your institution's academic integrity policies and local regulations. The developers are not responsible for any misuse of this software.

## 🚀 Features

### Stealth Capabilities
- **Invisible during screen sharing**: UI elements are designed to be undetectable in most screen capture scenarios
- **Emergency hide functions**: Multiple ways to instantly hide all traces
- **Auto-hide on focus loss**: Automatically hides when window loses focus
- **No browser UI modifications**: Operates without visible extension icons or popups
- **Memory clearing**: Automatic cleanup of traces and history

### User Interface
- **Hotkey activation**: Global keyboard shortcuts for discreet operation
- **Hidden overlays**: Transparent UI elements that don't interfere with normal browsing
- **Quick input mode**: Fast question submission via keyboard shortcuts
- **Discreet response display**: AI responses shown in subtle, hidden panels

### AI Integration
- **Multiple AI providers**: Support for OpenAI, Anthropic Claude, and custom APIs
- **Optimized responses**: Configured for concise, relevant answers
- **Secure API handling**: Encrypted storage of API keys
- **Error handling**: Graceful failure modes to avoid detection

## 🔧 Installation

1. **Download the extension files** to a local directory
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer mode** (toggle in top-right corner)
4. **Click "Load unpacked"** and select the extension directory
5. **Configure your AI API key** through the extension popup

## ⌨️ Hotkeys

| Shortcut | Action |
|----------|--------|
| `Ctrl+Shift+A` (or `Cmd+Shift+A` on Mac) | Toggle assistant |
| `Ctrl+Shift+Q` (or `Cmd+Shift+Q` on Mac) | Quick query mode |
| `Ctrl+Shift+H` | Emergency hide all |
| `Escape` | Hide current panel |
| `Enter` | Submit query (in input mode) |

## 🛠️ Configuration

### API Setup
1. Click the extension icon to open settings
2. Choose your AI provider (OpenAI, Anthropic, or Custom)
3. Enter your API key
4. Test the connection
5. Save configuration

### Supported AI Providers
- **OpenAI**: Requires OpenAI API key (GPT-3.5/GPT-4)
- **Anthropic**: Requires Anthropic API key (Claude)
- **Custom**: Configure your own API endpoint

## 🔒 Security Features

### Stealth Mechanisms
- **Transparent overlays**: UI elements use transparency and positioning to avoid screen capture
- **Focus detection**: Automatically hides when screen sharing is detected
- **Memory management**: Clears traces and history automatically
- **Emergency protocols**: Multiple failsafes for instant hiding

### Privacy Protection
- **Local storage**: API keys stored locally in encrypted format
- **No tracking**: No data collection or external tracking
- **Minimal permissions**: Only requests necessary browser permissions
- **Secure communication**: All API calls use HTTPS encryption

## 📁 File Structure

```
stealth-assistant/
├── manifest.json          # Extension configuration
├── background.js          # Service worker for API calls
├── content.js            # Main stealth UI logic
├── popup.html            # Settings interface
├── popup.js              # Settings functionality
├── stealth-overlay.html  # Advanced overlay UI
└── README.md             # This file
```

## 🧪 Testing

### Stealth Mode Testing
1. Open the extension settings
2. Click "Test Stealth Mode"
3. Try screen sharing to verify invisibility
4. Test emergency hide functions

### Connection Testing
1. Configure your API key
2. Click "Test Connection"
3. Verify AI responses are working

## ⚡ Usage Tips

### Best Practices
- **Test thoroughly** before important use
- **Use emergency hide** shortcuts when needed
- **Keep queries concise** for faster responses
- **Clear data regularly** for privacy

### Stealth Techniques
- Use during natural typing pauses
- Position response panels in less visible areas
- Utilize auto-hide timers
- Practice emergency hide shortcuts

## 🔧 Troubleshooting

### Common Issues
- **Extension not loading**: Check developer mode is enabled
- **API errors**: Verify API key and internet connection
- **Hotkeys not working**: Check for conflicts with other extensions
- **UI visible in screen share**: Test with different screen capture software

### Debug Mode
Enable Chrome DevTools and check the console for error messages:
1. Right-click on any page
2. Select "Inspect"
3. Go to Console tab
4. Look for extension-related errors

## 🚨 Ethical Considerations

### Responsible Use
- **Academic integrity**: Ensure compliance with institutional policies
- **Legal compliance**: Follow local laws and regulations
- **Fair use**: Consider the ethical implications of AI assistance
- **Transparency**: Be honest about AI assistance when required

### Limitations
- Not foolproof against all detection methods
- Requires stable internet connection
- API costs may apply for AI services
- May not work with all screen sharing software

## 🔄 Updates and Maintenance

### Regular Maintenance
- Update API keys as needed
- Clear stored data periodically
- Test stealth features regularly
- Monitor for browser updates that might affect functionality

### Version Updates
- Check for new versions regularly
- Backup configuration before updates
- Test functionality after updates

## 📞 Support

For technical issues or questions:
1. Check the troubleshooting section
2. Review browser console for errors
3. Verify API key configuration
4. Test with different websites

## 📄 License

This project is for educational purposes only. Use responsibly and in compliance with applicable laws and regulations.

---

**Remember**: This tool is designed for personal educational assistance. Always ensure your use complies with academic integrity policies and local regulations.
