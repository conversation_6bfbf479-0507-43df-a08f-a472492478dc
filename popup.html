<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stealth Assistant Settings</title>
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            color: #888;
        }
        
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #007acc;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            font-size: 12px;
            color: #ccc;
            margin-bottom: 5px;
        }
        
        input[type="password"], input[type="text"], select {
            width: 100%;
            padding: 8px;
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 4px;
            color: #fff;
            font-size: 13px;
            box-sizing: border-box;
        }
        
        input[type="password"]:focus, input[type="text"]:focus, select:focus {
            outline: none;
            border-color: #007acc;
        }
        
        .btn {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }
        
        .btn:hover {
            background: #0086e6;
        }
        
        .btn-secondary {
            background: #444;
        }
        
        .btn-secondary:hover {
            background: #555;
        }
        
        .status {
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 10px;
        }
        
        .status.success {
            background: rgba(0, 255, 0, 0.1);
            color: #90ee90;
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .status.error {
            background: rgba(255, 0, 0, 0.1);
            color: #ff9999;
            border: 1px solid rgba(255, 0, 0, 0.3);
        }
        
        .hotkeys {
            font-size: 11px;
            color: #888;
            line-height: 1.4;
        }
        
        .hotkey {
            background: #333;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        .warning {
            background: rgba(255, 165, 0, 0.1);
            border: 1px solid rgba(255, 165, 0, 0.3);
            color: #ffcc99;
            padding: 10px;
            border-radius: 4px;
            font-size: 11px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">🥷 Stealth Assistant</div>
        <div class="subtitle">Discreet AI assistance</div>
    </div>
    
    <div class="warning">
        ⚠️ This extension is for personal educational use only. Ensure compliance with your institution's policies.
    </div>
    
    <div class="section">
        <div class="section-title">🔑 AI Configuration</div>
        
        <div class="input-group">
            <label for="apiProvider">AI Provider:</label>
            <select id="apiProvider">
                <option value="openai">OpenAI (GPT)</option>
                <option value="anthropic">Anthropic (Claude)</option>
                <option value="custom">Custom API</option>
            </select>
        </div>
        
        <div class="input-group">
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" placeholder="Enter your API key">
        </div>
        
        <button class="btn" id="saveConfig">Save Configuration</button>
        <button class="btn btn-secondary" id="testConnection">Test Connection</button>
        
        <div id="configStatus" class="status" style="display: none;"></div>
    </div>
    
    <div class="section">
        <div class="section-title">⌨️ Hotkeys</div>
        <div class="hotkeys">
            <div><span class="hotkey">Ctrl+Shift+A</span> - Toggle assistant</div>
            <div><span class="hotkey">Ctrl+Shift+Q</span> - Quick query</div>
            <div><span class="hotkey">Ctrl+Shift+H</span> - Emergency hide</div>
            <div><span class="hotkey">Esc</span> - Hide current panel</div>
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">🛡️ Stealth Features</div>
        <div class="hotkeys">
            • Invisible overlays during screen sharing<br>
            • Auto-hide on window focus loss<br>
            • No browser UI modifications<br>
            • Automatic trace clearing<br>
            • Emergency hide shortcuts
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">🧪 Test Mode</div>
        <button class="btn" id="testStealth">Test Stealth Mode</button>
        <button class="btn btn-secondary" id="clearData">Clear All Data</button>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
