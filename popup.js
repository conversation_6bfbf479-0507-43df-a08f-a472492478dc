// Popup script for Stealth Assistant configuration
class StealthConfig {
  constructor() {
    this.setupEventListeners();
    this.loadConfiguration();
  }

  setupEventListeners() {
    document.getElementById('saveConfig').addEventListener('click', () => {
      this.saveConfiguration();
    });

    document.getElementById('testConnection').addEventListener('click', () => {
      this.testConnection();
    });

    document.getElementById('testStealth').addEventListener('click', () => {
      this.testStealthMode();
    });

    document.getElementById('clearData').addEventListener('click', () => {
      this.clearAllData();
    });

    // Auto-save on API key change
    document.getElementById('apiKey').addEventListener('input', () => {
      this.autoSave();
    });
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.local.get(['ai_api_key', 'api_provider']);
      
      if (result.ai_api_key) {
        document.getElementById('apiKey').value = result.ai_api_key;
      }
      
      if (result.api_provider) {
        document.getElementById('apiProvider').value = result.api_provider;
      }
    } catch (error) {
      this.showStatus('Error loading configuration', 'error');
    }
  }

  async saveConfiguration() {
    const apiKey = document.getElementById('apiKey').value.trim();
    const apiProvider = document.getElementById('apiProvider').value;

    if (!apiKey) {
      this.showStatus('Please enter an API key', 'error');
      return;
    }

    try {
      await chrome.storage.local.set({
        'ai_api_key': apiKey,
        'api_provider': apiProvider
      });

      // Notify background script
      await chrome.runtime.sendMessage({
        action: 'save-api-key',
        apiKey: apiKey,
        provider: apiProvider
      });

      this.showStatus('Configuration saved successfully!', 'success');
    } catch (error) {
      this.showStatus('Error saving configuration', 'error');
    }
  }

  async testConnection() {
    const apiKey = document.getElementById('apiKey').value.trim();
    const apiProvider = document.getElementById('apiProvider').value;

    if (!apiKey) {
      this.showStatus('Please enter an API key first', 'error');
      return;
    }

    // Save configuration first
    await this.saveConfiguration();

    this.showStatus('Testing connection...', 'success');

    // Disable button during test
    const testBtn = document.getElementById('testConnection');
    testBtn.disabled = true;
    testBtn.textContent = 'Testing...';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'get-ai-response',
        query: 'Test - respond with just "Connection OK"'
      });

      if (response.success) {
        this.showStatus('✅ Connection successful! AI responded: ' + response.response, 'success');
      } else {
        // Enhanced error messages
        let errorMsg = response.error;
        if (errorMsg.includes('Rate limit')) {
          errorMsg = 'Rate limit exceeded. Wait a moment and try again.';
        } else if (errorMsg.includes('Invalid API key')) {
          errorMsg = 'Invalid API key. Please check your key.';
        } else if (errorMsg.includes('API key not configured')) {
          errorMsg = 'API key not saved properly. Try saving again.';
        }
        this.showStatus(`❌ Connection failed: ${errorMsg}`, 'error');
      }
    } catch (error) {
      this.showStatus(`❌ Connection error: ${error.message}`, 'error');
    }

    // Re-enable button
    testBtn.disabled = false;
    testBtn.textContent = 'Test Connection';
  }

  async testStealthMode() {
    try {
      // Get active tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      // Send test message to content script
      await chrome.tabs.sendMessage(tab.id, {
        action: 'show-quick-input'
      });

      this.showStatus('Stealth mode activated on current tab', 'success');
      
      // Close popup after a short delay
      setTimeout(() => {
        window.close();
      }, 1000);
    } catch (error) {
      this.showStatus('Error activating stealth mode', 'error');
    }
  }

  async clearAllData() {
    if (!confirm('Are you sure you want to clear all data? This will remove your API key and settings.')) {
      return;
    }

    try {
      await chrome.storage.local.clear();
      document.getElementById('apiKey').value = '';
      document.getElementById('apiProvider').value = 'openai';
      this.showStatus('All data cleared', 'success');
    } catch (error) {
      this.showStatus('Error clearing data', 'error');
    }
  }

  async autoSave() {
    // Debounced auto-save
    clearTimeout(this.autoSaveTimeout);
    this.autoSaveTimeout = setTimeout(() => {
      this.saveConfiguration();
    }, 2000);
  }

  showStatus(message, type) {
    const statusEl = document.getElementById('configStatus');
    statusEl.textContent = message;
    statusEl.className = `status ${type}`;
    statusEl.style.display = 'block';

    // Auto-hide after 3 seconds
    setTimeout(() => {
      statusEl.style.display = 'none';
    }, 3000);
  }
}

// Initialize configuration when popup loads
document.addEventListener('DOMContentLoaded', () => {
  new StealthConfig();
});
